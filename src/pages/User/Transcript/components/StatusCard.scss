// Status color variables
$color-queued: #FF8E00;
$color-inprogress: #FFC700;
$color-failed: #DC0000;
$color-unavailable: #3B60E4;
$color-not-available: #3B60E4;

// Lighter border colors (60% opacity)
$color-queued-light: rgba(255, 142, 0, 0.9);
$color-inprogress-light: rgba(255, 199, 0, 0.9);
$color-failed-light: rgba(220, 0, 0, 0.9);
$color-unavailable-light: rgba(59, 96, 228, 0.9);
$color-not-available-light: rgba(59, 96, 228, 0.9);

// Circular background colors (20% opacity for subtle background)
$color-queued-bg: rgba(255, 142, 0, 0.9);
$color-inprogress-bg: rgba(255, 199, 0, 0.9);
$color-failed-bg: rgba(220, 0, 0, 0.9);
$color-unavailable-bg: rgba(59, 96, 228, 0.9);
$color-not-available-bg: rgba(59, 96, 228, 0.9);

// // Spinning animation for in-progress icons
// @keyframes spin {
//   from {
//     transform: rotate(0deg);
//   }
//   to {
//     transform: rotate(360deg);
//   }
// }

.status-card {
  background: #ffffff;
  border-radius: 7.674px;
  padding: 2.3rem;
  text-align: center;
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
  border: 2px solid;
  display: flex;
  flex-direction: column;
  align-items: center;

  &-outer {
    display: flex;
    flex-direction: column;
    gap: 0.7rem;
    align-items: center;
  }

  &-icon {
    font-size: 1.4rem;
    margin-bottom: 0.3rem;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 2.8rem;
    height: 2.8rem;
    border-radius: 50%;
    background: #f5f5f5; // Default background
    position: relative;

    // Make icons white and position them on top
    svg {
      color: white !important;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 2;
      width: 1.4rem;
      height: 1.4rem;
    }

    // // Spinning animation for in-progress icons
    // .spinning {
    //   animation: spin 2s linear infinite;
    // }
  }

  &-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
  }

  &-description {
    font-size: 0.7rem;
    color: #222;
    line-height: 1.3;
    margin-bottom: 0;
  }

  // Status-specific styles
  &-queued {
    border-color: $color-queued-light;
    .status-card-title { color: $color-queued; }
    .status-card-icon { 
      background: $color-queued-bg; 
      svg { color: white !important; }
    }
  }
  &-transcription_inprogress, &-in_process, &-analysis_inprogress, &-inprocess {
    border-color: $color-inprogress-light;
    .status-card-title { color: $color-inprogress; }
    .status-card-icon { 
      background: $color-inprogress-bg; 
      svg { color: white !important; }
    }
  }
  &-failed, &-analysis_failed, &-transcription_failed {
    border-color: $color-failed-light;
    .status-card-title { color: $color-failed; }
    .status-card-icon { 
      background: $color-failed-bg; 
      svg { color: white !important; }
    }
  }
  &-unavailable, &-not_available, &-not\ available {
    border-color: $color-unavailable-light;
    .status-card-title { color: $color-unavailable; }
    .status-card-icon { 
      background: $color-unavailable-bg; 
      svg { color: white !important; }
    }
  }
}

// Media Queries for all screen sizes
@media (max-width: 1200px) {
  .status-card {
    max-width: 450px;
    padding: 0.9rem 2.3rem;
    
    &-icon {
      font-size: 1.3rem;
      width: 2.6rem;
      height: 2.6rem;

      svg {
        width: 1.2rem;
        height: 1.2rem;
      }
    }
    
    &-title {
      font-size: 0.95rem;
    }
    
    &-description {
      font-size: 0.85rem;
    }
  }
}

@media (max-width: 992px) {
  .status-card {
    max-width: 420px;
    padding: 0.8rem 2.1rem;
    
    &-icon {
      font-size: 1.2rem;
      width: 2.4rem;
      height: 2.4rem;

      svg {
        width: 1rem;
        height: 1rem;
      }
    }
    
    &-title {
      font-size: 0.9rem;
    }
    
    &-description {
      font-size: 0.8rem;
    }
  }
}

@media (max-width: 768px) {
  .status-card {
    max-width: 380px;
    padding: 0.7rem 1.8rem;
    
    &-outer {
      gap: 0.6rem;
    }
    
    &-icon {
      font-size: 1.1rem;
      margin-bottom: 0.2rem;
      width: 2.2rem;
      height: 2.2rem;

      svg {
        width: 0.8rem;
        height: 0.8rem;
      }
    }
    
    &-title {
      font-size: 0.85rem;
      margin-bottom: 0.2rem;
    }
    
    &-description {
      font-size: 0.75rem;
      line-height: 1.2;
    }
  }
}

@media (max-width: 576px) {
  .status-card {
    max-width: 340px;
    padding: 0.6rem 1.5rem;
    
    &-outer {
      gap: 0.5rem;
    }
    
    &-icon {
      font-size: 1rem;
      margin-bottom: 0.2rem;
      width: 2rem;
      height: 2rem;

      svg {
        width: 0.8rem;
        height: 0.8rem;
      }
    }
    
    &-title {
      font-size: 0.8rem;
      margin-bottom: 0.2rem;
    }
    
    &-description {
      font-size: 0.7rem;
      line-height: 1.2;
    }
  }
}

@media (max-width: 480px) {
  .status-card {
    max-width: 300px;
    padding: 0.5rem 1.3rem;
    
    &-outer {
      gap: 0.4rem;
    }
    
    &-icon {
      font-size: 0.9rem;
      margin-bottom: 0.2rem;
      width: 1.8rem;
      height: 1.8rem;

      svg {
        width: 0.8rem;
        height: 0.8rem;
      }
    }
    
    &-title {
      font-size: 0.75rem;
      margin-bottom: 0.2rem;
    }
    
    &-description {
      font-size: 0.65rem;
      line-height: 1.1;
    }
  }
}

@media (max-width: 360px) {
  .status-card {
    max-width: 280px;
    padding: 0.4rem 1.1rem;
    
    &-outer {
      gap: 0.3rem;
    }
    
    &-icon {
      font-size: 0.8rem;
      margin-bottom: 0.1rem;
      width: 1.6rem;
      height: 1.6rem;

      svg {
        width: 0.8rem;
        height: 0.8rem;
      }
    }
    
    &-title {
      font-size: 0.7rem;
      margin-bottom: 0.1rem;
    }
    
    &-description {
      font-size: 0.6rem;
      line-height: 1.1;
    }
  }
}